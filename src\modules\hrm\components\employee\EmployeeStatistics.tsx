/**
 * Component thống kê nhân viên
 */
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Users, UserCheck, UserX, UserPlus, Clock, Award } from 'lucide-react';

import { Card, Typography } from '@/shared/components/common';
import ListOverviewCard from '@/shared/components/widgets/ListOverviewCard/ListOverviewCard';
import { OverviewCardProps } from '@/shared/components/widgets/OverviewCard/OverviewCard.types';

import { useEmployeeStatistics } from '../../hooks/useEmployeeStatistics';
import { DepartmentDistribution, EmploymentTypeDistribution } from '../../services/employee-statistics.service';

/**
 * Component hiển thị thống kê nhân viên
 */
const EmployeeStatistics: React.FC = () => {
  const { t } = useTranslation(['hrm', 'common']);
  const { data: statistics, isLoading } = useEmployeeStatistics();

  // Tạo dữ liệu cho overview cards
  const overviewData: OverviewCardProps[] = useMemo(() => {
    if (!statistics) return [];

    return [
      {
        title: t('hrm:employee.statistics.totalEmployees', 'Tổng số nhân viên') as string,
        value: statistics.totalEmployees,
        description: t('hrm:employee.statistics.totalEmployeesDesc', 'Tất cả nhân viên') as string,
        icon: Users,
        color: 'blue' as const,
      },
      {
        title: t('hrm:employee.statistics.totalUsers', 'Tổng số tài khoản') as string,
        value: statistics.totalUsers,
        description: t('hrm:employee.statistics.totalUsersDesc', 'Tài khoản đã tạo') as string,
        icon: UserCheck,
        color: 'green' as const,
      },
      {
        title: t('hrm:employee.statistics.activeEmployees', 'Nhân viên hoạt động') as string,
        value: statistics.activeEmployees,
        description: `${((statistics.activeEmployees / statistics.totalEmployees) * 100).toFixed(1)}% tổng số`,
        icon: UserCheck,
        color: 'green' as const,
      },
      {
        title: t('hrm:employee.statistics.inactiveEmployees', 'Nhân viên không hoạt động') as string,
        value: statistics.inactiveEmployees,
        description: `${((statistics.inactiveEmployees / statistics.totalEmployees) * 100).toFixed(1)}% tổng số`,
        icon: UserX,
        color: 'red' as const,
      },
      {
        title: t('hrm:employee.statistics.newEmployeesThisMonth', 'Nhân viên mới tháng này') as string,
        value: statistics.newEmployeesThisMonth,
        description: t('hrm:employee.statistics.newEmployeesDesc', 'Gia nhập trong tháng') as string,
        icon: UserPlus,
        color: 'purple' as const,
      },
      {
        title: t('hrm:employee.statistics.employeesOnProbation', 'Nhân viên thử việc') as string,
        value: statistics.employeesOnProbation,
        description: t('hrm:employee.statistics.probationDesc', 'Đang trong thời gian thử việc') as string,
        icon: Clock,
        color: 'orange' as const,
      },
    ];
  }, [statistics, t]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <Typography variant="h2" className="mb-2">
          {t('hrm:employee.statistics.title', 'Thống kê nhân viên') as string}
        </Typography>
        <Typography variant="body1" className="text-muted-foreground">
          {t('hrm:employee.statistics.description', 'Tổng quan về tình hình nhân sự trong công ty') as string}
        </Typography>
      </div>

      {/* Overview Cards */}
      <ListOverviewCard
        items={overviewData}
        maxColumns={{ xs: 1, sm: 2, md: 3, lg: 6, xl: 6 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 2, md: 2, lg: 4, xl: 4 }}
        gap={4}
        isLoading={isLoading}
        skeletonCount={6}
      />

      {/* Additional Statistics */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Department Distribution */}
          <Card className="p-6">
            <Typography variant="h3" className="mb-4">
              {t('hrm:employee.statistics.departmentDistribution', 'Phân bố theo phòng ban') as string}
            </Typography>
            <div className="space-y-3">
              {statistics.departmentDistribution.map((dept: DepartmentDistribution, index: number) => (
                <div key={index} className="flex justify-between items-center">
                  <Typography variant="body2" className="font-medium">
                    {dept.departmentName}
                  </Typography>
                  <div className="flex items-center gap-2">
                    <Typography variant="body2" className="text-muted-foreground">
                      {dept.employeeCount} nhân viên
                    </Typography>
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full"
                        style={{
                          width: `${(dept.employeeCount / statistics.totalEmployees) * 100}%`,
                        }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          {/* Employment Type Distribution */}
          <Card className="p-6">
            <Typography variant="h3" className="mb-4">
              {t('hrm:employee.statistics.employmentTypeDistribution', 'Phân bố theo loại hợp đồng') as string}
            </Typography>
            <div className="space-y-3">
              {statistics.employmentTypeDistribution.map((type: EmploymentTypeDistribution, index: number) => (
                <div key={index} className="flex justify-between items-center">
                  <Typography variant="body2" className="font-medium">
                    {t(`hrm:employee.employmentType.${type.type}`, type.type) as string}
                  </Typography>
                  <div className="flex items-center gap-2">
                    <Typography variant="body2" className="text-muted-foreground">
                      {type.count} nhân viên
                    </Typography>
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-500 h-2 rounded-full"
                        style={{
                          width: `${(type.count / statistics.totalEmployees) * 100}%`,
                        }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>
      )}

      {/* Additional Metrics */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="p-6 text-center">
            <Award className="w-8 h-8 mx-auto mb-3 text-blue-500" />
            <Typography variant="h3" className="mb-1">
              {statistics.averageServiceYears.toFixed(1)} năm
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {t('hrm:employee.statistics.averageServiceYears', 'Thâm niên trung bình') as string}
            </Typography>
          </Card>

          <Card className="p-6 text-center">
            <Clock className="w-8 h-8 mx-auto mb-3 text-orange-500" />
            <Typography variant="h3" className="mb-1">
              {statistics.upcomingProbationEnds}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {t('hrm:employee.statistics.upcomingProbationEnds', 'Sắp hết thử việc') as string}
            </Typography>
          </Card>

          <Card className="p-6 text-center">
            <UserPlus className="w-8 h-8 mx-auto mb-3 text-purple-500" />
            <Typography variant="h3" className="mb-1">
              {((statistics.newEmployeesThisMonth / statistics.totalEmployees) * 100).toFixed(1)}%
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {t('hrm:employee.statistics.growthRate', 'Tỷ lệ tăng trưởng tháng') as string}
            </Typography>
          </Card>
        </div>
      )}
    </div>
  );
};

export default EmployeeStatistics;
